{
    // structure describes the shape of inspection model
    // service methods describes the methods in inspection service
    // create a new bucket 'inspections' (do the necessary to the appsettings and to the code base to ensure its handled like the other buckets e.g autocreation )
    // in this bucket for each inspections is a folder with two sub folders: signatures and images
    // when creating the inspection the inspected by signature should be stored in the signatures sub folder and images stored in images sub folder
    
    "structure": {// has IAuditableEntity and softdelete property
        "inspections": [ // an array of object of shape below
            {
                "description": "string",
                "isTrue": "bool",
                "remarks": "string",
                "imageFiles": [ // an array of FileMetadata
                    "FileMetadata"
                ]
            }
        ],
        "approved": "bool",
        "comments": "string",
        "signatureFile": "FileMetadata"
    },
    "service methods": {
        "create inspections": {
            "args": [
                "inspections",
                "approved",
                "comments",
                "inspected by"
            ],
            "actions": [
                "creates inspections using items in args",
                "takes the inspected by worker and copy signatureFile to inspections, and IAuditableEntity 'CreatedBy'"
            ]
        }
    }
}