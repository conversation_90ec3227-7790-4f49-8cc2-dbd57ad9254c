using HotChocolate.Types;

namespace GraphQLApi.GraphQL.Types
{
    /// <summary>
    /// Input type for creating a worker with training and document data
    /// </summary>
    /// 
    public class UpdateWorkerWithTrainingInput
    {
        public int Id { get; set; }
        public string? Name { get; set; }
        public string? Company { get; set; }
        public string? NationalId { get; set; }
        public DateOnly? DateOfBirth { get; set; }
        public List<WorkerTrainingInput>? Trainings { get; set; }
        public List<int>? TradeIds { get; set; }
        public List<int>? SkillIds { get; set; }
        public int? ManHours { get; set; }
        public double? Rating { get; set; }
        public string? Gender { get; set; }
        public string? PhoneNumber { get; set; }
        public string? MpesaNumber { get; set; }
        public string? Email { get; set; }
        public DateTime? InductionDate { get; set; }
        public DateTime? MedicalCheckDate { get; set; }
        public IFile? ProfilePicture { get; set; }
        public IFile? Signature { get; set; }
        public List<GraphQLApi.GraphQL.Types.DocumentFileInput>? Documents { get; set; }
    }
    public class CreateWorkerWithTrainingInput
    {
        public required string Name { get; set; }
        public required string Company { get; set; }
        public required string NationalId { get; set; }
        public required string Gender { get; set; }
        public required string PhoneNumber { get; set; }
        public DateOnly? DateOfBirth { get; set; }
        public List<WorkerTrainingInput>? Trainings { get; set; }
        public List<int>? TradeIds { get; set; }
        public List<int>? SkillIds { get; set; }
        public string? MpesaNumber { get; set; }
        public string? Email { get; set; }
        public DateTime? InductionDate { get; set; }
        public DateTime? MedicalCheckDate { get; set; }
        public required IFile ProfilePicture { get; set; }
        public IFile? Signature { get; set; }
        public List<DocumentFileInput>? Documents { get; set; }
    }

    /// <summary>
    /// Input type for training with associated documents
    /// </summary>
    public class WorkerTrainingInput
    {
        public int TrainingId { get; set; }
        public string? Notes { get; set; }
        public List<DocumentFileInput>? Documents { get; set; }
    }

    /// <summary>
    /// Input type for adding documents to an existing worker-training relationship
    /// </summary>
    public class AddDocumentToWorkerTrainingInput
    {
        public int WorkerId { get; set; }
        public int TrainingId { get; set; }
        public List<DocumentFileInput> Documents { get; set; } = new();
    }

    /// <summary>
    /// Input type for creating a worker-training relationship with documents
    /// </summary>
    public class CreateWorkerTrainingInput
    {
        public int WorkerId { get; set; }
        public int TrainingId { get; set; }
        public string? Notes { get; set; }
        public List<DocumentFileInput>? Documents { get; set; }
    }
}
