﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GraphQLApi.Migrations
{
    /// <inheritdoc />
    public partial class MoveDocumentsToWorkerTraining : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DocumentFiles_Trainings_TrainingId",
                table: "DocumentFiles");

            migrationBuilder.DropIndex(
                name: "IX_DocumentFiles_TrainingId",
                table: "DocumentFiles");

            migrationBuilder.AddColumn<DateTime>(
                name: "AssignedDate",
                table: "WorkerTraining",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedAt",
                table: "WorkerTraining",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "CreatedBy",
                table: "WorkerTraining",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletedAt",
                table: "WorkerTraining",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DeletedBy",
                table: "WorkerTraining",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "WorkerTraining",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "Notes",
                table: "WorkerTraining",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedAt",
                table: "WorkerTraining",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UpdatedBy",
                table: "WorkerTraining",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "WorkerTrainingTrainingId",
                table: "DocumentFiles",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "WorkerTrainingWorkerId",
                table: "DocumentFiles",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_WorkerTraining_AssignedDate",
                table: "WorkerTraining",
                column: "AssignedDate");

            migrationBuilder.CreateIndex(
                name: "IX_WorkerTraining_CreatedAt",
                table: "WorkerTraining",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_WorkerTraining_IsDeleted",
                table: "WorkerTraining",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_WorkerTraining_WorkerId",
                table: "WorkerTraining",
                column: "WorkerId");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentFiles_WorkerTraining",
                table: "DocumentFiles",
                columns: new[] { "EntityType", "WorkerTrainingWorkerId", "WorkerTrainingTrainingId" },
                filter: "[EntityType] = 'WorkerTraining' AND [WorkerTrainingWorkerId] IS NOT NULL AND [WorkerTrainingTrainingId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentFiles_WorkerTrainingTrainingId_WorkerTrainingWorkerId",
                table: "DocumentFiles",
                columns: new[] { "WorkerTrainingTrainingId", "WorkerTrainingWorkerId" });

            migrationBuilder.AddForeignKey(
                name: "FK_DocumentFiles_WorkerTraining_WorkerTrainingTrainingId_WorkerTrainingWorkerId",
                table: "DocumentFiles",
                columns: new[] { "WorkerTrainingTrainingId", "WorkerTrainingWorkerId" },
                principalTable: "WorkerTraining",
                principalColumns: new[] { "WorkerId", "TrainingId" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DocumentFiles_WorkerTraining_WorkerTrainingTrainingId_WorkerTrainingWorkerId",
                table: "DocumentFiles");

            migrationBuilder.DropIndex(
                name: "IX_WorkerTraining_AssignedDate",
                table: "WorkerTraining");

            migrationBuilder.DropIndex(
                name: "IX_WorkerTraining_CreatedAt",
                table: "WorkerTraining");

            migrationBuilder.DropIndex(
                name: "IX_WorkerTraining_IsDeleted",
                table: "WorkerTraining");

            migrationBuilder.DropIndex(
                name: "IX_WorkerTraining_WorkerId",
                table: "WorkerTraining");

            migrationBuilder.DropIndex(
                name: "IX_DocumentFiles_WorkerTraining",
                table: "DocumentFiles");

            migrationBuilder.DropIndex(
                name: "IX_DocumentFiles_WorkerTrainingTrainingId_WorkerTrainingWorkerId",
                table: "DocumentFiles");

            migrationBuilder.DropColumn(
                name: "AssignedDate",
                table: "WorkerTraining");

            migrationBuilder.DropColumn(
                name: "CreatedAt",
                table: "WorkerTraining");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "WorkerTraining");

            migrationBuilder.DropColumn(
                name: "DeletedAt",
                table: "WorkerTraining");

            migrationBuilder.DropColumn(
                name: "DeletedBy",
                table: "WorkerTraining");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "WorkerTraining");

            migrationBuilder.DropColumn(
                name: "Notes",
                table: "WorkerTraining");

            migrationBuilder.DropColumn(
                name: "UpdatedAt",
                table: "WorkerTraining");

            migrationBuilder.DropColumn(
                name: "UpdatedBy",
                table: "WorkerTraining");

            migrationBuilder.DropColumn(
                name: "WorkerTrainingTrainingId",
                table: "DocumentFiles");

            migrationBuilder.DropColumn(
                name: "WorkerTrainingWorkerId",
                table: "DocumentFiles");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentFiles_TrainingId",
                table: "DocumentFiles",
                column: "TrainingId");

            migrationBuilder.AddForeignKey(
                name: "FK_DocumentFiles_Trainings_TrainingId",
                table: "DocumentFiles",
                column: "TrainingId",
                principalTable: "Trainings",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
