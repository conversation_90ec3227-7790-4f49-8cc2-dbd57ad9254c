using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using HotChocolate;

namespace GraphQLApi.Services
{
    public class WorkerTrainingService : IWorkerTrainingService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly ILogger<WorkerTrainingService> _logger;

        public WorkerTrainingService(
            IDbContextFactory<AppDbContext> contextFactory,
            ILogger<WorkerTrainingService> logger)
        {
            _contextFactory = contextFactory;
            _logger = logger;
        }

        public async Task<WorkerTraining?> GetWorkerTrainingAsync(int workerId, int trainingId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.WorkerTrainings
                .Include(wt => wt.Worker)
                .Include(wt => wt.Training)
                .Include(wt => wt.DocumentFiles)
                    .ThenInclude(df => df.FileMetadata)
                .FirstOrDefaultAsync(wt => wt.WorkerId == workerId && wt.TrainingId == trainingId);
        }

        public async Task<IEnumerable<WorkerTraining>> GetWorkerTrainingsAsync(int workerId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.WorkerTrainings
                .Include(wt => wt.Training)
                .Include(wt => wt.DocumentFiles)
                    .ThenInclude(df => df.FileMetadata)
                .Where(wt => wt.WorkerId == workerId)
                .ToListAsync();
        }

        public async Task<IEnumerable<WorkerTraining>> GetTrainingWorkersAsync(int trainingId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.WorkerTrainings
                .Include(wt => wt.Worker)
                .Include(wt => wt.DocumentFiles)
                    .ThenInclude(df => df.FileMetadata)
                .Where(wt => wt.TrainingId == trainingId)
                .ToListAsync();
        }

        public async Task<WorkerTraining> CreateWorkerTrainingAsync(int workerId, int trainingId, string? notes = null)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            // Check if the relationship already exists
            var existing = await context.WorkerTrainings
                .FirstOrDefaultAsync(wt => wt.WorkerId == workerId && wt.TrainingId == trainingId);

            if (existing != null)
            {
                throw new GraphQLException(new Error(
                    "Validation",
                    $"Worker {workerId} is already assigned to training {trainingId}.")
                );
            }

            // Verify worker and training exist
            var worker = await context.Workers.FindAsync(workerId);
            var training = await context.Trainings.FindAsync(trainingId);

            if (worker == null)
                throw new GraphQLException(new Error("Validation", $"Worker with ID {workerId} not found."));

            if (training == null)
                throw new GraphQLException(new Error("Validation", $"Training with ID {trainingId} not found."));

            var workerTraining = new WorkerTraining
            {
                WorkerId = workerId,
                TrainingId = trainingId,
                Notes = notes,
                AssignedDate = DateTime.UtcNow
            };

            context.WorkerTrainings.Add(workerTraining);
            await context.SaveChangesAsync();

            return await GetWorkerTrainingAsync(workerId, trainingId) ?? workerTraining;
        }

        public async Task<WorkerTraining> AddDocumentToWorkerTrainingAsync(int workerId, int trainingId, int documentId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var workerTraining = await context.WorkerTrainings
                .Include(wt => wt.DocumentFiles)
                .FirstOrDefaultAsync(wt => wt.WorkerId == workerId && wt.TrainingId == trainingId);

            if (workerTraining == null)
                throw new ArgumentException($"Worker training relationship not found for worker {workerId} and training {trainingId}");

            var document = await context.DocumentFiles
                .FirstOrDefaultAsync(d => d.Id == documentId);

            if (document == null)
                throw new ArgumentException($"Document with ID {documentId} not found");

            if (!workerTraining.DocumentFiles.Contains(document))
            {
                workerTraining.DocumentFiles.Add(document);
                await context.SaveChangesAsync();
            }

            return workerTraining;
        }

        public async Task<bool> RemoveWorkerTrainingAsync(int workerId, int trainingId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var workerTraining = await context.WorkerTrainings
                .FirstOrDefaultAsync(wt => wt.WorkerId == workerId && wt.TrainingId == trainingId);

            if (workerTraining == null)
                return false;

            context.WorkerTrainings.Remove(workerTraining);
            await context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RemoveDocumentFromWorkerTrainingAsync(int workerId, int trainingId, int documentId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var workerTraining = await context.WorkerTrainings
                .Include(wt => wt.DocumentFiles)
                .FirstOrDefaultAsync(wt => wt.WorkerId == workerId && wt.TrainingId == trainingId);

            if (workerTraining == null)
                return false;

            var document = workerTraining.DocumentFiles.FirstOrDefault(d => d.Id == documentId);
            if (document == null)
                return false;

            workerTraining.DocumentFiles.Remove(document);
            await context.SaveChangesAsync();
            return true;
        }
    }
}
