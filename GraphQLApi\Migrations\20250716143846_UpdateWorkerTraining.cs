﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GraphQLApi.Migrations
{
    /// <inheritdoc />
    public partial class UpdateWorkerTraining : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DocumentFiles_WorkerTraining_WorkerTrainingTrainingId_WorkerTrainingWorkerId",
                table: "DocumentFiles");

            migrationBuilder.DropIndex(
                name: "IX_DocumentFiles_Training",
                table: "DocumentFiles");

            migrationBuilder.DropIndex(
                name: "IX_DocumentFiles_Worker",
                table: "DocumentFiles");

            migrationBuilder.DropIndex(
                name: "IX_DocumentFiles_WorkerTraining",
                table: "DocumentFiles");

            migrationBuilder.DropIndex(
                name: "IX_DocumentFiles_WorkerTrainingTrainingId_WorkerTrainingWorkerId",
                table: "DocumentFiles");

            migrationBuilder.DropColumn(
                name: "EntityType",
                table: "DocumentFiles");

            migrationBuilder.DropColumn(
                name: "TrainingId",
                table: "DocumentFiles");

            migrationBuilder.DropColumn(
                name: "WorkerTrainingTrainingId",
                table: "DocumentFiles");

            migrationBuilder.DropColumn(
                name: "WorkerTrainingWorkerId",
                table: "DocumentFiles");

            migrationBuilder.CreateTable(
                name: "WorkerTrainingDocuments",
                columns: table => new
                {
                    DocumentFilesId = table.Column<int>(type: "int", nullable: false),
                    WorkerTrainingWorkerId = table.Column<int>(type: "int", nullable: false),
                    WorkerTrainingTrainingId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkerTrainingDocuments", x => new { x.DocumentFilesId, x.WorkerTrainingWorkerId, x.WorkerTrainingTrainingId });
                    table.ForeignKey(
                        name: "FK_WorkerTrainingDocuments_DocumentFiles_DocumentFilesId",
                        column: x => x.DocumentFilesId,
                        principalTable: "DocumentFiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_WorkerTrainingDocuments_WorkerTraining_WorkerTrainingWorkerId_WorkerTrainingTrainingId",
                        columns: x => new { x.WorkerTrainingWorkerId, x.WorkerTrainingTrainingId },
                        principalTable: "WorkerTraining",
                        principalColumns: new[] { "WorkerId", "TrainingId" },
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_WorkerTrainingDocuments_WorkerTrainingWorkerId_WorkerTrainingTrainingId",
                table: "WorkerTrainingDocuments",
                columns: new[] { "WorkerTrainingWorkerId", "WorkerTrainingTrainingId" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "WorkerTrainingDocuments");

            migrationBuilder.AddColumn<string>(
                name: "EntityType",
                table: "DocumentFiles",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TrainingId",
                table: "DocumentFiles",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "WorkerTrainingTrainingId",
                table: "DocumentFiles",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "WorkerTrainingWorkerId",
                table: "DocumentFiles",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_DocumentFiles_Training",
                table: "DocumentFiles",
                columns: new[] { "EntityType", "TrainingId" },
                filter: "[EntityType] = 'Training' AND [TrainingId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentFiles_Worker",
                table: "DocumentFiles",
                columns: new[] { "EntityType", "WorkerId" },
                filter: "[EntityType] = 'Worker' AND [WorkerId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentFiles_WorkerTraining",
                table: "DocumentFiles",
                columns: new[] { "EntityType", "WorkerTrainingWorkerId", "WorkerTrainingTrainingId" },
                filter: "[EntityType] = 'WorkerTraining' AND [WorkerTrainingWorkerId] IS NOT NULL AND [WorkerTrainingTrainingId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentFiles_WorkerTrainingTrainingId_WorkerTrainingWorkerId",
                table: "DocumentFiles",
                columns: new[] { "WorkerTrainingTrainingId", "WorkerTrainingWorkerId" });

            migrationBuilder.AddForeignKey(
                name: "FK_DocumentFiles_WorkerTraining_WorkerTrainingTrainingId_WorkerTrainingWorkerId",
                table: "DocumentFiles",
                columns: new[] { "WorkerTrainingTrainingId", "WorkerTrainingWorkerId" },
                principalTable: "WorkerTraining",
                principalColumns: new[] { "WorkerId", "TrainingId" });
        }
    }
}
