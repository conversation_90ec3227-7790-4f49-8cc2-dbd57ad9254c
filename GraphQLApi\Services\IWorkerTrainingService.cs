using Shared.GraphQL.Models;

namespace GraphQLApi.Services
{
    public interface IWorkerTrainingService
    {
        Task<WorkerTraining?> GetWorkerTrainingAsync(int workerId, int trainingId);
        Task<IEnumerable<WorkerTraining>> GetWorkerTrainingsAsync(int workerId);
        Task<IEnumerable<WorkerTraining>> GetTrainingWorkersAsync(int trainingId);
        Task<WorkerTraining> CreateWorkerTrainingAsync(int workerId, int trainingId, string? notes = null);
        Task<WorkerTraining> AddDocumentToWorkerTrainingAsync(int workerId, int trainingId, int documentId);
        Task<bool> RemoveWorkerTrainingAsync(int workerId, int trainingId);
        Task<bool> RemoveDocumentFromWorkerTrainingAsync(int workerId, int trainingId, int documentId);
    }
}
