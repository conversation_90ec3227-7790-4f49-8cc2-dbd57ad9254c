using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using Shared.GraphQL.Models.Permits;
using System.Reflection;
using Shared.Interfaces;
using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Metadata;

namespace GraphQLApi.Data
{
    public class AppDbContext : DbContext
    {
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        public DbSet<Worker> Workers { get; set; }
        public DbSet<WorkerAttendance> WorkerAttendances { get; set; }
        public DbSet<ToolboxSession> ToolboxSessions { get; set; }
        public DbSet<ToolboxAttendance> ToolboxAttendances { get; set; }
        public DbSet<Training> Trainings { get; set; }
        public DbSet<WorkerTraining> WorkerTrainings { get; set; }
        public DbSet<Trade> Trades { get; set; }
        public DbSet<Skill> Skills { get; set; }
        public DbSet<WorkerTrainingHistory> WorkerTrainingHistory { get; set; }
        // public DbSet<Shared.GraphQL.Models.Task> Tasks { get; set; }
        public DbSet<Equipment> Equipment { get; set; }
        public DbSet<Incident> Incidents { get; set; }
        public DbSet<FileMetadata> FileMetadata { get; set; }
        public DbSet<DocumentFile> DocumentFiles { get; set; }

        // Job System
        public DbSet<Job> Jobs { get; set; }
        public DbSet<Hazard> Hazards { get; set; }
        public DbSet<ControlMeasure> ControlMeasures { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<Toolbox> Toolboxes { get; set; }

        // Permit System
        public DbSet<Permit> Permits { get; set; }
        public DbSet<GeneralWorkPermit> GeneralWorkPermits { get; set; }
        public DbSet<ExcavationWorkPermit> ExcavationWorkPermits { get; set; }
        public DbSet<WorkAtHeightPermit> WorkAtHeightPermits { get; set; }
        public DbSet<ConfinedSpacePermit> ConfinedSpacePermits { get; set; }
        public DbSet<HotWorkPermit> HotWorkPermits { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

            // Apply global query filters for soft delete
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                if (typeof(ISoftDeletable).IsAssignableFrom(entityType.ClrType) &&
                    entityType.BaseType == null) // Only apply to root entities in inheritance hierarchy
                {
                    var method = typeof(AppDbContext)
                        .GetMethod(nameof(GetSoftDeleteFilter), BindingFlags.NonPublic | BindingFlags.Static)
                        ?.MakeGenericMethod(entityType.ClrType);
                    var filter = method?.Invoke(null, Array.Empty<object>());
                    entityType.SetQueryFilter((LambdaExpression)filter!);
                }
            }
            modelBuilder.Entity<Training>()
    .HasIndex(u => u.Name)
    .IsUnique();
            modelBuilder.Entity<Trade>()
    .HasIndex(u => u.Name)
    .IsUnique();
            modelBuilder.Entity<Skill>()
    .HasIndex(u => u.Name)
    .IsUnique();

            base.OnModelCreating(modelBuilder);
        }

        private static LambdaExpression GetSoftDeleteFilter<TEntity>() where TEntity : class, ISoftDeletable
        {
            Expression<Func<TEntity, bool>> filter = x => !x.IsDeleted;
            return filter;
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateAuditFields();
            HandleSoftDelete();
            return base.SaveChangesAsync(cancellationToken);
        }

        private void UpdateAuditFields()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.Entity is IAuditableEntity && (
                    e.State == EntityState.Added ||
                    e.State == EntityState.Modified));

            foreach (var entityEntry in entries)
            {
                var entity = (IAuditableEntity)entityEntry.Entity;

                if (entityEntry.State == EntityState.Added)
                {
                    entity.CreatedAt = DateTime.UtcNow;
                    entity.CreatedBy ??= "System";
                }

                entity.UpdatedAt = DateTime.UtcNow;
                entity.UpdatedBy ??= "System";
            }
        }

        private void HandleSoftDelete()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.Entity is ISoftDeletable && e.State == EntityState.Deleted);

            foreach (var entityEntry in entries)
            {
                var entity = (ISoftDeletable)entityEntry.Entity;
                entityEntry.State = EntityState.Modified;
                entity.IsDeleted = true;
                entity.DeletedAt = DateTime.UtcNow;
                entity.DeletedBy ??= "System";

                // Cascade soft delete to related child entities
                CascadeSoftDelete(entityEntry, new HashSet<object>(), 0);
            }
        }

        private const int MaxCascadeDepth = 10;

        // private void CascadeSoftDelete(
        //     Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry parentEntry,
        //     HashSet<object>? visited = null,
        //     int depth = 0)
        // {
        //     if (depth > MaxCascadeDepth)
        //         return;

        //     visited ??= new HashSet<object>();
        //     var entity = parentEntry.Entity;

        //     // Prevent cycles
        //     if (!visited.Add(entity))
        //         return;

        //     var navigations = parentEntry.Navigations;

        //     foreach (var navigationEntry in navigations)
        //     {
        //         // Ensure navigation is loaded
        //         if (!navigationEntry.IsLoaded)
        //         {
        //             navigationEntry.Load();
        //         }

        //         if (!navigationEntry.Metadata.IsCollection)
        //         {
        //             var childEntity = navigationEntry.CurrentValue;
        //             if (childEntity is ISoftDeletable softDeletableChild && !softDeletableChild.IsDeleted)
        //             {
        //                 var childEntry = ChangeTracker.Entries()
        //                     .FirstOrDefault(e => e.Entity == childEntity);
        //                 if (childEntry != null)
        //                 {
        //                     childEntry.State = EntityState.Modified;
        //                     softDeletableChild.IsDeleted = true;
        //                     softDeletableChild.DeletedAt = DateTime.UtcNow;
        //                     softDeletableChild.DeletedBy ??= "System";
        //                     // Recursively cascade with updated parameters
        //                     CascadeSoftDelete(childEntry, visited, depth + 1);
        //                 }
        //             }
        //         }
        //         else
        //         {
        //             var children = navigationEntry.CurrentValue as System.Collections.IEnumerable;
        //             if (children != null)
        //             {
        //                 foreach (var child in children)
        //                 {
        //                     if (child is ISoftDeletable softDeletableChild && !softDeletableChild.IsDeleted)
        //                     {
        //                         var childEntry = ChangeTracker.Entries()
        //                             .FirstOrDefault(e => e.Entity == child);
        //                         if (childEntry != null)
        //                         {
        //                             childEntry.State = EntityState.Modified;
        //                             softDeletableChild.IsDeleted = true;
        //                             softDeletableChild.DeletedAt = DateTime.UtcNow;
        //                             softDeletableChild.DeletedBy ??= "System";
        //                             // Recursively cascade
        //                             CascadeSoftDelete(childEntry);
        //                         }
        //                     }
        //                 }
        //             }
        //         }
        //     }
        // }
        private void CascadeSoftDelete(
    Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry parentEntry,
    HashSet<object>? visited = null,
    int depth = 0)
        {
            if (depth > MaxCascadeDepth)
                return;

            visited ??= new HashSet<object>();
            var entity = parentEntry.Entity;

            // Prevent cycles
            if (!visited.Add(entity))
                return;

            // ✅ Optional: Log the entity being processed
            Console.WriteLine($"[CascadeSoftDelete] Visiting {entity.GetType().Name} (depth {depth})");

            var navigations = parentEntry.Navigations;

            foreach (var navigationEntry in navigations)
            {
                try
                {
                    if (navigationEntry.Metadata?.PropertyInfo == null)
                        continue;

                    // ✅ Log the navigation being accessed
                    Console.WriteLine($"[CascadeSoftDelete] Inspecting navigation: {navigationEntry.Metadata.Name}");

                    if (!navigationEntry.IsLoaded && navigationEntry.Metadata is INavigation)
                    {
                        navigationEntry.Load();
                        Console.WriteLine($"[CascadeSoftDelete] Loaded navigation: {navigationEntry.Metadata.Name}");
                    }

                    var value = navigationEntry.CurrentValue;
                    if (value == null)
                        continue;

                    if (!navigationEntry.Metadata.IsCollection)
                    {
                        if (value is ISoftDeletable softDeletableChild && !softDeletableChild.IsDeleted)
                        {
                            var childEntry = ChangeTracker.Entries()
                                .FirstOrDefault(e => e.Entity == value);
                            if (childEntry != null)
                            {
                                Console.WriteLine($"[CascadeSoftDelete] Soft deleting child entity: {value.GetType().Name}");
                                childEntry.State = EntityState.Modified;
                                softDeletableChild.IsDeleted = true;
                                softDeletableChild.DeletedAt = DateTime.UtcNow;
                                softDeletableChild.DeletedBy ??= "System";
                                CascadeSoftDelete(childEntry, visited, depth + 1);
                            }
                        }
                    }
                    else
                    {
                        if (value is System.Collections.IEnumerable children)
                        {
                            foreach (var child in children)
                            {
                                if (child is ISoftDeletable softDeletableChild && !softDeletableChild.IsDeleted)
                                {
                                    var childEntry = ChangeTracker.Entries()
                                        .FirstOrDefault(e => e.Entity == child);
                                    if (childEntry != null)
                                    {
                                        Console.WriteLine($"[CascadeSoftDelete] Soft deleting child in collection: {child.GetType().Name}");
                                        childEntry.State = EntityState.Modified;
                                        softDeletableChild.IsDeleted = true;
                                        softDeletableChild.DeletedAt = DateTime.UtcNow;
                                        softDeletableChild.DeletedBy ??= "System";
                                        CascadeSoftDelete(childEntry, visited, depth + 1);
                                    }
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[CascadeSoftDelete] Error in navigation '{navigationEntry.Metadata?.Name}': {ex.Message}");
                }
            }
        }

    }
}