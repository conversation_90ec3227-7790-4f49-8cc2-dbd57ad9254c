using Shared.Interfaces;

namespace Shared.GraphQL.Models
{
    /// <summary>
    /// Entity representing the many-to-many relationship between Worker and Training
    /// with associated document files
    /// </summary>
    public class WorkerTraining : IAuditableEntity, ISoftDeletable
    {
        /// <summary>
        /// Foreign key to Worker
        /// </summary>
        public int WorkerId { get; set; }

        /// <summary>
        /// Navigation property to Worker
        /// </summary>
        public virtual Worker Worker { get; set; } = null!;

        /// <summary>
        /// Foreign key to Training
        /// </summary>
        public int TrainingId { get; set; }

        /// <summary>
        /// Navigation property to Training
        /// </summary>
        public virtual Training Training { get; set; } = null!;

        /// <summary>
        /// Collection of document files associated with this worker-training relationship
        /// </summary>
        public virtual ICollection<DocumentFile> DocumentFiles { get; set; } = new List<DocumentFile>();

        /// <summary>
        /// Optional notes about this worker's training assignment
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// Date when the worker was assigned to this training
        /// </summary>
        public DateTime AssignedDate { get; set; } = DateTime.UtcNow;

        // IAuditableEntity implementation
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;

        // ISoftDeletable implementation
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }
}
