﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GraphQLApi.Migrations
{
    /// <inheritdoc />
    public partial class UpdateWorkerDocument3 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DocumentFiles_Workers_WorkerId",
                table: "DocumentFiles");

            migrationBuilder.DropForeignKey(
                name: "FK_WorkerDocuments_DocumentFiles_DocumentsId",
                table: "WorkerDocuments");

            migrationBuilder.DropIndex(
                name: "IX_DocumentFiles_WorkerId",
                table: "DocumentFiles");

            migrationBuilder.DropColumn(
                name: "WorkerId",
                table: "DocumentFiles");

            migrationBuilder.RenameColumn(
                name: "DocumentsId",
                table: "WorkerDocuments",
                newName: "DocumentFilesId");

            migrationBuilder.AddForeignKey(
                name: "FK_WorkerDocuments_DocumentFiles_DocumentFilesId",
                table: "WorkerDocuments",
                column: "DocumentFilesId",
                principalTable: "DocumentFiles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_WorkerDocuments_DocumentFiles_DocumentFilesId",
                table: "WorkerDocuments");

            migrationBuilder.RenameColumn(
                name: "DocumentFilesId",
                table: "WorkerDocuments",
                newName: "DocumentsId");

            migrationBuilder.AddColumn<int>(
                name: "WorkerId",
                table: "DocumentFiles",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_DocumentFiles_WorkerId",
                table: "DocumentFiles",
                column: "WorkerId");

            migrationBuilder.AddForeignKey(
                name: "FK_DocumentFiles_Workers_WorkerId",
                table: "DocumentFiles",
                column: "WorkerId",
                principalTable: "Workers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_WorkerDocuments_DocumentFiles_DocumentsId",
                table: "WorkerDocuments",
                column: "DocumentsId",
                principalTable: "DocumentFiles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
