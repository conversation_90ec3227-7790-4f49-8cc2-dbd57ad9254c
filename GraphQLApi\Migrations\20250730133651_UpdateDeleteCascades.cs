﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GraphQLApi.Migrations
{
    /// <inheritdoc />
    public partial class UpdateDeleteCascades : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_WorkerTraining_Trainings_TrainingId",
                table: "WorkerTraining");

            migrationBuilder.DropForeignKey(
                name: "FK_WorkerTraining_Workers_WorkerId",
                table: "WorkerTraining");

            migrationBuilder.AddForeignKey(
                name: "FK_WorkerTraining_Trainings_TrainingId",
                table: "WorkerTraining",
                column: "TrainingId",
                principalTable: "Trainings",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_WorkerTraining_Workers_WorkerId",
                table: "WorkerTraining",
                column: "WorkerId",
                principalTable: "Workers",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_WorkerTraining_Trainings_TrainingId",
                table: "WorkerTraining");

            migrationBuilder.DropForeignKey(
                name: "FK_WorkerTraining_Workers_WorkerId",
                table: "WorkerTraining");

            migrationBuilder.AddForeignKey(
                name: "FK_WorkerTraining_Trainings_TrainingId",
                table: "WorkerTraining",
                column: "TrainingId",
                principalTable: "Trainings",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_WorkerTraining_Workers_WorkerId",
                table: "WorkerTraining",
                column: "WorkerId",
                principalTable: "Workers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
