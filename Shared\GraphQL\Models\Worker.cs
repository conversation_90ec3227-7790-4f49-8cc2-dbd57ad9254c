using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Shared.Interfaces;
using Shared.Enums;

namespace Shared.GraphQL.Models
{
    public class Worker : IAuditableEntity, ISoftDeletable
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Company { get; set; }
        public DateOnly? DateOfBirth { get; set; }

        public int ManHours { get; set; }
        public DateTime? InductionDate { get; set; }

        // File metadata relationships
        /// <summary>
        /// Foreign key to profile picture file metadata
        /// </summary>
        public int? ProfilePictureFileId { get; set; }

        /// <summary>
        /// Navigation property to profile picture file metadata
        /// </summary>
        public virtual FileMetadata? ProfilePictureFile { get; set; }

        /// <summary>
        /// Foreign key to signature file metadata
        /// </summary>
        public int? SignatureFileId { get; set; }

        /// <summary>
        /// Navigation property to signature file metadata
        /// </summary>
        public virtual FileMetadata? SignatureFile { get; set; }

        /// <summary>
        /// Collection of document files for this worker
        /// </summary>
        public virtual ICollection<DocumentFile> DocumentFiles { get; set; } = new List<DocumentFile>();
        public DateTime? MedicalCheckDate { get; set; }
        public double Rating { get; set; }
        public string Gender { get; set; }
        public string NationalId { get; set; }
        public string PhoneNumber { get; set; }
        public string? Email { get; set; }
        public string? MpesaNumber { get; set; }

        // Navigation Properties
        public ICollection<Training> Trainings { get; set; } = new List<Training>();
        // public ICollection<DocumentFile> Documents { get; set; } = new List<DocumentFile>();
        public ICollection<WorkerTraining> WorkerTrainings { get; set; } = new List<WorkerTraining>();
        public ICollection<Trade> Trades { get; set; } = [];
        public ICollection<Skill> Skills { get; set; } = [];
        public ICollection<WorkerTrainingHistory> TrainingHistory { get; set; } = [];
        public ICollection<Incident> Incidents { get; set; } = [];

        // Computed Properties
        public int? Age => DateOfBirth.HasValue
            ? DateTime.Today.Year - DateOfBirth.Value.Year - (DateTime.Today.DayOfYear < DateOfBirth.Value.DayOfYear ? 1 : 0)
            : null;

        public int TrainingsCompleted => TrainingHistory?.Count(t => t.Status == TrainingStatus.Completed) ?? 0;

        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; }

        // Soft Delete Fields
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }
}