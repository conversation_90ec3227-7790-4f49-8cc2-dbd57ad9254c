using HotChocolate.Types;

namespace GraphQLApi.GraphQL.Types
{
    // GraphQL Input Types for Permit operations
    public class CreateGeneralWorkPermitInput
    {
        public int JobId { get; set; }
        public string PTWRefNumber { get; set; } = string.Empty;
        public string ProjectName { get; set; } = string.Empty;
        public DateTime StartingDateTime { get; set; }
        public DateTime EndingDateTime { get; set; }
        public string Description { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string Hazards { get; set; } = string.Empty;
        public string PrecautionsRequired { get; set; } = string.Empty;
        public string PPE { get; set; } = string.Empty;
        public string Isolation { get; set; } = string.Empty;
        public PermitIssuerInput PermitIssuer { get; set; } = null!;
        public SignOffInput SignOff { get; set; } = null!;
    }

    public class CreateHotWorkPermitInput
    {
        public int JobId { get; set; }
        public string PTWRefNumber { get; set; } = string.Empty;
        public string ProjectName { get; set; } = string.Empty;
        public DateTime StartingDateTime { get; set; }
        public DateTime EndingDateTime { get; set; }
        public string Description { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string Hazards { get; set; } = string.Empty;
        public string PrecautionsRequired { get; set; } = string.Empty;
        public string PPE { get; set; } = string.Empty;
        public string NatureOfWork { get; set; } = string.Empty;
        public string FireExtinguishers { get; set; } = string.Empty;
        public FireSafetySupervisorInput FireSafetySupervisor { get; set; } = null!;
        public PermitIssuerInput PermitIssuer { get; set; } = null!;
        public SignOffInput SignOff { get; set; } = null!;
    }

    public class CreateExcavationWorkPermitInput
    {
        public int JobId { get; set; }
        public string PTWRefNumber { get; set; } = string.Empty;
        public string ProjectName { get; set; } = string.Empty;
        public DateTime StartingDateTime { get; set; }
        public DateTime EndingDateTime { get; set; }
        public string Description { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string Hazards { get; set; } = string.Empty;
        public string PrecautionsRequired { get; set; } = string.Empty;
        public string PPE { get; set; } = string.Empty;
        public string DepthOfExcavation { get; set; } = string.Empty;
        public string ProtectionSystems { get; set; } = string.Empty;
        public string ListOfEquipmentToBeUsed { get; set; } = string.Empty;
        public string Inspections { get; set; } = string.Empty;
        public InspectionAuthorizationInput InspectionAuthorization { get; set; } = null!;
        public PermitIssuerInput PermitIssuer { get; set; } = null!;
        public SignOffInput SignOff { get; set; } = null!;
    }

    public class CreateWorkAtHeightPermitInput
    {
        public int JobId { get; set; }
        public string PTWRefNumber { get; set; } = string.Empty;
        public string ProjectName { get; set; } = string.Empty;
        public DateTime StartingDateTime { get; set; }
        public DateTime EndingDateTime { get; set; }
        public string Description { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string Hazards { get; set; } = string.Empty;
        public string PrecautionsRequired { get; set; } = string.Empty;
        public string PPE { get; set; } = string.Empty;
        public string ModeOfAccessToBeUsed { get; set; } = string.Empty;
        public string Inspections { get; set; } = string.Empty;
        public InspectionAuthorizationInput InspectionAuthorization { get; set; } = null!;
        public PermitIssuerInput PermitIssuer { get; set; } = null!;
        public SignOffInput SignOff { get; set; } = null!;
    }

    public class CreateConfinedSpacePermitInput
    {
        public int JobId { get; set; }
        public string PTWRefNumber { get; set; } = string.Empty;
        public string ProjectName { get; set; } = string.Empty;
        public DateTime StartingDateTime { get; set; }
        public DateTime EndingDateTime { get; set; }
        public string Description { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string Hazards { get; set; } = string.Empty;
        public string PrecautionsRequired { get; set; } = string.Empty;
        public string PPE { get; set; } = string.Empty;
        public bool WorkersHaveBeenTrained { get; set; }
        public string NameOfTrainingOrganization { get; set; } = string.Empty;
        public AtmosphericReadingInput TopReading { get; set; } = null!;
        public AtmosphericReadingInput MidReading { get; set; } = null!;
        public AtmosphericReadingInput BottomReading { get; set; } = null!;
        public string EmergencyGuidelines { get; set; } = string.Empty;
        public TaskObserverInput TaskObserver { get; set; } = null!;
        public PermitIssuerInput PermitIssuer { get; set; } = null!;
        public SignOffInput SignOff { get; set; } = null!;
    }

    public class PermitIssuerInput
    {
        public List<CompetentPersonInput> CompetentPersons { get; set; } = new();
        public List<AuthorisedPersonInput> AuthorisedPersons { get; set; } = new();
    }

    public class PermitReturnInput
    {
        public List<CompetentPersonInput> CompetentPersons { get; set; } = new();
        public List<AuthorisedPersonInput> AuthorisedPersons { get; set; } = new();
    }

    public class SignOffInput
    {
        public DateTime DateTime { get; set; }
        public List<PermitWorkerInput> Workers { get; set; } = new();
    }

    public class CompetentPersonInput
    {
        public int WorkerId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string SignatureFileId { get; set; } = string.Empty;
        public DateTime SignedAt { get; set; }
    }

    public class AuthorisedPersonInput
    {
        public int WorkerId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string SignatureFileId { get; set; } = string.Empty;
        public DateTime SignedAt { get; set; }
    }

    public class PermitWorkerInput
    {
        public int WorkerId { get; set; }
        public string Designation { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string SignatureFileId { get; set; } = string.Empty;
        public DateTime SignedAt { get; set; }
    }

    public class FireSafetySupervisorInput
    {
        public int WorkerId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string SignatureFileId { get; set; } = string.Empty;
        public DateTime SignedAt { get; set; }
    }

    public class InspectionAuthorizationInput
    {
        public string NameOfInspector { get; set; } = string.Empty;
        public string Designation { get; set; } = string.Empty;
        public DateTime DateOfInspection { get; set; }
        public string Comments { get; set; } = string.Empty;
    }

    public class AtmosphericReadingInput
    {
        public string Oxygen { get; set; } = string.Empty;
        public string Explosive { get; set; } = string.Empty;
        public string Toxic { get; set; } = string.Empty;
        public string Co2 { get; set; } = string.Empty;
    }

    public class TaskObserverInput
    {
        public int WorkerId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string SignatureFileId { get; set; } = string.Empty;
        public DateTime SignedAt { get; set; }
    }

    public class ReturnPermitInput
    {
        public int PermitId { get; set; }
        public PermitReturnInput PermitReturn { get; set; } = null!;
    }

    // Output types for GraphQL
    public class ToolboxRiskAssessment
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public List<ToolboxRiskAssessmentHazard> Hazards { get; set; } = new();
    }

    public class ToolboxRiskAssessmentHazard
    {
        public int Id { get; set; }
        public string Description { get; set; } = string.Empty;
        public List<ToolboxRiskAssessmentControlMeasure> ControlMeasures { get; set; } = new();
    }

    public class ToolboxRiskAssessmentControlMeasure
    {
        public int Id { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    // GraphQL Type definitions for the output types
    public class ToolboxRiskAssessmentType : ObjectType<ToolboxRiskAssessment>
    {
        protected override void Configure(IObjectTypeDescriptor<ToolboxRiskAssessment> descriptor)
        {
            descriptor.Field(t => t.Id).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.Title).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.Hazards).Type<ToolboxRiskAssessmentHazardType>();
        }
    }

    public class ToolboxRiskAssessmentHazardType : ObjectType<ToolboxRiskAssessmentHazard>
    {
        protected override void Configure(IObjectTypeDescriptor<ToolboxRiskAssessmentHazard> descriptor)
        {
            descriptor.Field(h => h.Id).Type<NonNullType<IntType>>();
            descriptor.Field(h => h.Description).Type<NonNullType<StringType>>();
            descriptor.Field(h => h.ControlMeasures).Type<ListType<ToolboxRiskAssessmentControlMeasureType>>();
        }
    }

    public class ToolboxRiskAssessmentControlMeasureType : ObjectType<ToolboxRiskAssessmentControlMeasure>
    {
        protected override void Configure(IObjectTypeDescriptor<ToolboxRiskAssessmentControlMeasure> descriptor)
        {
            descriptor.Field(cm => cm.Id).Type<NonNullType<IntType>>();
            descriptor.Field(cm => cm.Description).Type<NonNullType<StringType>>();
        }
    }
}
